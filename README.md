# MCP Weather Server

基于 Spring AI MCP Server WebFlux Starter 实现的天气查询 MCP (Model Context Protocol) 服务器，支持 SSE (Server-Sent Events) 模式。

## 项目简介

本项目使用 Spring AI MCP 框架构建了一个功能完整的天气查询服务，可以作为 MCP 服务器为 AI 应用（如 Claude Desktop）提供天气信息查询能力。

### 主要特性

- 🌤️ **完整的天气信息**：支持当前天气、天气预报查询
- 🌍 **多种查询方式**：支持城市名称和经纬度查询
- 🔄 **SSE 通信模式**：基于 Server-Sent Events 的 HTTP 通信
- 🌐 **免费 API**：使用 OpenMeteo 开源天气 API，无需申请密钥
- 🎯 **智能解析**：自动解析城市名称，支持中英文
- 📊 **丰富数据**：温度、湿度、风力、气压、紫外线等完整信息
- 🛠️ **易于集成**：标准 MCP 协议，可与任何支持 MCP 的 AI 应用集成

## 技术架构

### 核心技术栈

- **Spring Boot 3.5.4**：应用框架
- **Spring AI 1.0.1**：MCP 服务器实现
- **Spring WebFlux**：响应式 Web 框架（SSE 模式）
- **OpenMeteo API**：天气数据源
- **Jackson**：JSON 序列化/反序列化
- **Java 21**：运行环境

### 项目结构

```
src/main/java/com/mcp/weather/
├── McpWeatherApplication.java          # 主应用类
├── config/
│   └── WebClientConfig.java           # WebClient 配置
├── model/
│   ├── WeatherResponse.java           # 天气响应模型
│   └── GeocodingResponse.java          # 地理编码响应模型
├── service/
│   └── WeatherService.java            # 天气服务（MCP 工具）
└── util/
    └── WeatherCodeUtil.java           # 天气代码工具类

src/main/resources/
├── application.yml                     # 应用配置
└── ...

src/test/java/
└── com/mcp/weather/service/
    └── WeatherServiceTest.java        # 天气服务测试
```

## 核心功能

### MCP 工具列表

本项目提供了三个主要的天气查询工具，使用 `@Tool` 注解自动注册到 Spring AI MCP 服务器中：

1. **getCurrentWeather**
   - 描述：根据城市名称获取当前天气信息
   - 参数：cityName (城市名称，支持中英文)
   - 返回：详细的当前天气信息，包括温度、湿度、风力等

2. **getWeatherForecast**
   - 描述：根据城市名称获取未来7天的天气预报
   - 参数：cityName (城市名称), days (预报天数，1-7天，默认7天)
   - 返回：指定天数的天气预报

3. **getWeatherByCoordinates**
   - 描述：根据经纬度获取当前天气信息
   - 参数：latitude (纬度), longitude (经度)
   - 返回：指定坐标的天气信息

### 天气信息包含

- 🌡️ 温度（实际温度和体感温度）
- 🌤️ 天气状况（晴、多云、雨、雪等）
- 💧 相对湿度和湿度等级
- ☁️ 云量覆盖
- 💨 风向、风速、风力等级
- 🔽 大气压力
- 🌧️ 降水量
- ☀️ 紫外线指数和等级
- 🌅 日出日落时间

## 快速开始

### 环境要求

- Java 21 或更高版本
- Maven 3.6 或更高版本
- 网络连接（访问 OpenMeteo API）

### 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd mcp-weather

# 编译打包
mvn clean package -DskipTests
```

### 运行服务

```bash
# 启动 SSE 模式服务
java -jar target/mcp-weather-0.0.1-SNAPSHOT.jar

# 或使用 Maven 运行
mvn spring-boot:run
```

服务启动后将在 `http://localhost:8080` 提供 MCP 服务接口。

### 运行测试

```bash
mvn test
```

## 使用方式

### 1. 与 Claude Desktop 集成

编辑 Claude Desktop 配置文件：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

添加以下配置：

```json
{
  "mcpServers": {
    "weather": {
      "command": "java",
      "args": [
        "-jar",
        "/path/to/your/mcp-weather-0.0.1-SNAPSHOT.jar"
      ],
      "env": {}
    }
  }
}
```

**注意**：请将 `/path/to/your/mcp-weather-0.0.1-SNAPSHOT.jar` 替换为实际的 jar 文件完整路径。

### 2. 与其他 MCP 客户端集成

如果您的 MCP 客户端支持 HTTP/SSE 协议，可以连接到：
- **基础 URL**: `http://localhost:8080`
- **MCP 端点**: 由 Spring AI MCP 自动配置

### 3. 使用示例

在 Claude Desktop 中，您可以这样使用：

```
请查询北京今天的天气情况
```

```
帮我看看上海未来3天的天气预报
```

```
查询纽约的当前天气
```

## 配置说明

### 应用配置 (application.yml)

```yaml
# 服务器端口
server:
  port: 8080

spring:
  ai:
    mcp:
      server:
        name: mcp-weather-server
        version: 1.0.0
        instructions: "天气查询 MCP 服务器说明"
        capabilities:
          tool: true
          resource: false
          prompt: false
          completion: false

# 日志配置
logging:
  level:
    org.springframework.ai.mcp: DEBUG
    com.mcp.weather: DEBUG
```

### 环境变量

目前项目不需要额外的环境变量配置，因为使用的是免费的 OpenMeteo API。

## 开发指南

### 添加新的天气工具

1. 在 `WeatherService` 类中添加新方法
2. 使用 `@Tool` 注解标记方法
3. 使用 `@ToolParameter` 注解参数
4. 重新编译打包

示例：

```java
@Tool(description = "获取空气质量信息")
public String getAirQuality(
    @ToolParameter(description = "城市名称") String cityName) {
    // 实现逻辑
    return "空气质量信息";
}
```

### 自定义配置

可以通过修改 `application.yml` 或使用命令行参数来自定义配置：

```bash
java -jar mcp-weather-0.0.1-SNAPSHOT.jar \
  --server.port=9090 \
  --spring.ai.mcp.server.name=my-weather-server
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案：修改 server.port 配置或停止占用端口的程序
   ```

2. **网络连接失败**
   ```
   解决方案：检查网络连接，确保可以访问 api.open-meteo.com
   ```

3. **城市名称无法识别**
   ```
   解决方案：尝试使用英文城市名称或更具体的地名
   ```

4. **Claude Desktop 无法加载服务**
   ```
   解决方案：
   - 检查 jar 文件路径是否正确
   - 确保 Java 21+ 已安装
   - 查看 Claude Desktop 的错误日志
   ```

### 调试模式

启用详细日志：

```bash
java -jar mcp-weather-0.0.1-SNAPSHOT.jar \
  --logging.level.com.mcp.weather=DEBUG \
  --logging.level.org.springframework.ai.mcp=DEBUG
```

## API 文档

### OpenMeteo API

本项目使用以下 OpenMeteo API 端点：

- **地理编码**: `https://geocoding-api.open-meteo.com/v1/search`
- **天气数据**: `https://api.open-meteo.com/v1/forecast`

详细 API 文档：https://open-meteo.com/en/docs

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [Spring AI](https://docs.spring.io/spring-ai/reference/) - MCP 框架支持
- [OpenMeteo](https://open-meteo.com/) - 免费天气 API
- [Model Context Protocol](https://modelcontextprotocol.io/) - 协议规范

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发起 Discussion
- 邮件联系：[<EMAIL>]

---

**注意**：本项目仅供学习和研究使用，天气数据来源于 OpenMeteo，请遵守相关使用条款。
