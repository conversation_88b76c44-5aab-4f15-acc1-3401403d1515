package com.mcp.weather.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 天气服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
class WeatherServiceTest {
    
    @Autowired
    private WeatherService weatherService;
    
    @Test
    void testGetCurrentWeather() {
        // 测试获取杭州天气
        String result = weatherService.getCurrentWeather("杭州");
        assertNotNull(result);
        assertTrue(result.contains("杭州") || result.contains("Hangzhou"));
        System.out.println("杭州天气:");
        System.out.println(result);
    }
    
    @Test
    void testGetWeatherForecast() {
        // 测试获取上海天气预报
        String result = weatherService.getWeatherForecast("上海", 3);
        assertNotNull(result);
        assertTrue(result.contains("上海") || result.contains("Shanghai"));
        System.out.println("上海3天天气预报:");
        System.out.println(result);
    }
    
    @Test
    void testGetWeatherByCoordinates() {
        // 测试通过坐标获取天气（北京坐标）
        String result = weatherService.getWeatherByCoordinates(39.9042, 116.4074);
        assertNotNull(result);
        assertTrue(result.contains("指定位置"));
        System.out.println("坐标天气:");
        System.out.println(result);
    }
    
    @Test
    void testInvalidCity() {
        // 测试无效城市名称
        String result = weatherService.getCurrentWeather("不存在的城市名称123456");
        assertNotNull(result);
        assertTrue(result.contains("未找到"));
        System.out.println("无效城市测试:");
        System.out.println(result);
    }
}
