package com.mcp.weather.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.springframework.util.StringUtils;

/**
 * 中文转拼音
 *
 * <AUTHOR>
 */
public class PinyinUtil {
    private static final HanyuPinyinOutputFormat FORMAT;

    static {
        FORMAT = new HanyuPinyinOutputFormat();
        FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    /**
     * 中文转拼音
     *
     * @param chinese 中文字符串
     * @return 拼音字符串，空格分隔
     */
    public static String toPinyin(String chinese) {
        return toPinyin(chinese, "");
    }

    /**
     * 中文转拼音
     *
     * @param chinese 中文字符串
     * @param separator 拼音之间的分隔符
     * @return 拼音字符串
     */
    public static String toPinyin(String chinese, String separator) {
        if (!StringUtils.hasText(chinese)) {
            return "";
        }

        if (separator == null) {
            separator = "";
        }

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < chinese.length(); i++) {
            char c = chinese.charAt(i);

            if (isChinese(c)) {
                String pinyin = getCharPinyin(c);
                result.append(pinyin);
            } else {
                result.append(c);
            }

            // 添加分隔符（最后一个字符不添加）
            if (!separator.isEmpty() && i < chinese.length() - 1 && isChinese(c)) {
                char nextChar = chinese.charAt(i + 1);
                if (isChinese(nextChar)) {
                    result.append(separator);
                }
            }
        }

        return result.toString();
    }

    /**
     * 获取单个汉字的拼音
     *
     * @param c 汉字字符
     * @return 拼音字符串
     */
    private static String getCharPinyin(char c) {
        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0];
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            // 转换失败时返回原字符
        }
        return String.valueOf(c);
    }

    /**
     * 判断字符是否为中文字符
     *
     * @param c 字符
     * @return 是否为中文字符
     */
    private static boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }
}
