package com.mcp.weather.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 天气代码工具类
 * 根据 WMO Weather interpretation codes (WW) 标准
 */
public class WeatherCodeUtil {

    private static final Map<Integer, String> WEATHER_CODES = new HashMap<>();

    static {
        WEATHER_CODES.put(0, "晴朗");
        WEATHER_CODES.put(1, "主要晴朗");
        WEATHER_CODES.put(2, "部分多云");
        WEATHER_CODES.put(3, "阴天");
        WEATHER_CODES.put(45, "雾");
        WEATHER_CODES.put(48, "沉积霜雾");
        WEATHER_CODES.put(51, "小毛毛雨");
        WEATHER_CODES.put(53, "中等毛毛雨");
        WEATHER_CODES.put(55, "密集毛毛雨");
        WEATHER_CODES.put(56, "轻微冻毛毛雨");
        WEATHER_CODES.put(57, "密集冻毛毛雨");
        WEATHER_CODES.put(61, "小雨");
        WEATHER_CODES.put(63, "中雨");
        WEATHER_CODES.put(65, "大雨");
        WEATHER_CODES.put(66, "轻微冻雨");
        WEATHER_CODES.put(67, "大冻雨");
        WEATHER_CODES.put(71, "小雪");
        WEATHER_CODES.put(73, "中雪");
        WEATHER_CODES.put(75, "大雪");
        WEATHER_CODES.put(77, "雪粒");
        WEATHER_CODES.put(80, "小阵雨");
        WEATHER_CODES.put(81, "中阵雨");
        WEATHER_CODES.put(82, "大阵雨");
        WEATHER_CODES.put(85, "小阵雪");
        WEATHER_CODES.put(86, "大阵雪");
        WEATHER_CODES.put(95, "雷暴");
        WEATHER_CODES.put(96, "轻微冰雹雷暴");
        WEATHER_CODES.put(99, "大冰雹雷暴");
    }

    /**
     * 根据天气代码获取天气描述
     *
     * @param code 天气代码
     * @return 天气描述
     */
    public static String getWeatherDescription(int code) {
        return WEATHER_CODES.getOrDefault(code, "未知天气");
    }

    /**
     * 根据风向角度获取风向描述
     *
     * @param degree 风向角度 (0-360)
     * @return 风向描述
     */
    public static String getWindDirection(int degree) {
        if (degree >= 0 && degree < 22.5 || degree >= 337.5) {
            return "北风";
        } else if (degree >= 22.5 && degree < 67.5) {
            return "东北风";
        } else if (degree >= 67.5 && degree < 112.5) {
            return "东风";
        } else if (degree >= 112.5 && degree < 157.5) {
            return "东南风";
        } else if (degree >= 157.5 && degree < 202.5) {
            return "南风";
        } else if (degree >= 202.5 && degree < 247.5) {
            return "西南风";
        } else if (degree >= 247.5 && degree < 292.5) {
            return "西风";
        } else if (degree >= 292.5 && degree < 337.5) {
            return "西北风";
        } else {
            return "无风向";
        }
    }

    /**
     * 根据风速获取风力等级描述
     *
     * @param windSpeed 风速 (km/h)
     * @return 风力等级描述
     */
    public static String getWindLevel(double windSpeed) {
        if (windSpeed < 1) {
            return "无风 (0级)";
        } else if (windSpeed < 6) {
            return "软风 (1级)";
        } else if (windSpeed < 12) {
            return "轻风 (2级)";
        } else if (windSpeed < 20) {
            return "微风 (3级)";
        } else if (windSpeed < 29) {
            return "和风 (4级)";
        } else if (windSpeed < 39) {
            return "清劲风 (5级)";
        } else if (windSpeed < 50) {
            return "强风 (6级)";
        } else if (windSpeed < 62) {
            return "疾风 (7级)";
        } else if (windSpeed < 75) {
            return "大风 (8级)";
        } else if (windSpeed < 89) {
            return "烈风 (9级)";
        } else if (windSpeed < 103) {
            return "狂风 (10级)";
        } else if (windSpeed < 118) {
            return "暴风 (11级)";
        } else {
            return "飓风 (12级)";
        }
    }

    /**
     * 根据湿度获取湿度等级描述
     *
     * @param humidity 相对湿度 (%)
     * @return 湿度等级描述
     */
    public static String getHumidityLevel(int humidity) {
        if (humidity < 30) {
            return "干燥";
        } else if (humidity < 60) {
            return "适宜";
        } else if (humidity < 80) {
            return "潮湿";
        } else {
            return "非常潮湿";
        }
    }

    /**
     * 根据紫外线指数获取紫外线等级描述
     *
     * @param uvIndex 紫外线指数
     * @return 紫外线等级描述
     */
    public static String getUvLevel(double uvIndex) {
        if (uvIndex < 3) {
            return "低 (绿色)";
        } else if (uvIndex < 6) {
            return "中等 (黄色)";
        } else if (uvIndex < 8) {
            return "高 (橙色)";
        } else if (uvIndex < 11) {
            return "很高 (红色)";
        } else {
            return "极高 (紫色)";
        }
    }
}
