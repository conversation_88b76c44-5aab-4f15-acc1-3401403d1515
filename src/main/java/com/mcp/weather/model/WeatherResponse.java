package com.mcp.weather.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * OpenMeteo API 响应模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeatherResponse {
    
    private double latitude;
    private double longitude;
    
    @JsonProperty("generationtime_ms")
    private double generationTimeMs;
    
    @JsonProperty("utc_offset_seconds")
    private int utcOffsetSeconds;
    
    private String timezone;
    
    @JsonProperty("timezone_abbreviation")
    private String timezoneAbbreviation;
    
    private int elevation;
    
    @JsonProperty("current_units")
    private CurrentUnits currentUnits;
    
    private Current current;
    
    @JsonProperty("daily_units")
    private DailyUnits dailyUnits;
    
    private Daily daily;
    
    // Getters and Setters
    public double getLatitude() { return latitude; }
    public void setLatitude(double latitude) { this.latitude = latitude; }
    
    public double getLongitude() { return longitude; }
    public void setLongitude(double longitude) { this.longitude = longitude; }
    
    public double getGenerationTimeMs() { return generationTimeMs; }
    public void setGenerationTimeMs(double generationTimeMs) { this.generationTimeMs = generationTimeMs; }
    
    public int getUtcOffsetSeconds() { return utcOffsetSeconds; }
    public void setUtcOffsetSeconds(int utcOffsetSeconds) { this.utcOffsetSeconds = utcOffsetSeconds; }
    
    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }
    
    public String getTimezoneAbbreviation() { return timezoneAbbreviation; }
    public void setTimezoneAbbreviation(String timezoneAbbreviation) { this.timezoneAbbreviation = timezoneAbbreviation; }
    
    public int getElevation() { return elevation; }
    public void setElevation(int elevation) { this.elevation = elevation; }
    
    public CurrentUnits getCurrentUnits() { return currentUnits; }
    public void setCurrentUnits(CurrentUnits currentUnits) { this.currentUnits = currentUnits; }
    
    public Current getCurrent() { return current; }
    public void setCurrent(Current current) { this.current = current; }
    
    public DailyUnits getDailyUnits() { return dailyUnits; }
    public void setDailyUnits(DailyUnits dailyUnits) { this.dailyUnits = dailyUnits; }
    
    public Daily getDaily() { return daily; }
    public void setDaily(Daily daily) { this.daily = daily; }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CurrentUnits {
        private String time;
        private String interval;
        
        @JsonProperty("temperature_2m")
        private String temperature2m;
        
        @JsonProperty("relative_humidity_2m")
        private String relativeHumidity2m;
        
        @JsonProperty("apparent_temperature")
        private String apparentTemperature;
        
        @JsonProperty("is_day")
        private String isDay;
        
        private String precipitation;
        
        @JsonProperty("weather_code")
        private String weatherCode;
        
        @JsonProperty("cloud_cover")
        private String cloudCover;
        
        @JsonProperty("pressure_msl")
        private String pressureMsl;
        
        @JsonProperty("surface_pressure")
        private String surfacePressure;
        
        @JsonProperty("wind_speed_10m")
        private String windSpeed10m;
        
        @JsonProperty("wind_direction_10m")
        private String windDirection10m;
        
        @JsonProperty("wind_gusts_10m")
        private String windGusts10m;
        
        // Getters and Setters
        public String getTime() { return time; }
        public void setTime(String time) { this.time = time; }
        
        public String getInterval() { return interval; }
        public void setInterval(String interval) { this.interval = interval; }
        
        public String getTemperature2m() { return temperature2m; }
        public void setTemperature2m(String temperature2m) { this.temperature2m = temperature2m; }
        
        public String getRelativeHumidity2m() { return relativeHumidity2m; }
        public void setRelativeHumidity2m(String relativeHumidity2m) { this.relativeHumidity2m = relativeHumidity2m; }
        
        public String getApparentTemperature() { return apparentTemperature; }
        public void setApparentTemperature(String apparentTemperature) { this.apparentTemperature = apparentTemperature; }
        
        public String getIsDay() { return isDay; }
        public void setIsDay(String isDay) { this.isDay = isDay; }
        
        public String getPrecipitation() { return precipitation; }
        public void setPrecipitation(String precipitation) { this.precipitation = precipitation; }
        
        public String getWeatherCode() { return weatherCode; }
        public void setWeatherCode(String weatherCode) { this.weatherCode = weatherCode; }
        
        public String getCloudCover() { return cloudCover; }
        public void setCloudCover(String cloudCover) { this.cloudCover = cloudCover; }
        
        public String getPressureMsl() { return pressureMsl; }
        public void setPressureMsl(String pressureMsl) { this.pressureMsl = pressureMsl; }
        
        public String getSurfacePressure() { return surfacePressure; }
        public void setSurfacePressure(String surfacePressure) { this.surfacePressure = surfacePressure; }
        
        public String getWindSpeed10m() { return windSpeed10m; }
        public void setWindSpeed10m(String windSpeed10m) { this.windSpeed10m = windSpeed10m; }
        
        public String getWindDirection10m() { return windDirection10m; }
        public void setWindDirection10m(String windDirection10m) { this.windDirection10m = windDirection10m; }
        
        public String getWindGusts10m() { return windGusts10m; }
        public void setWindGusts10m(String windGusts10m) { this.windGusts10m = windGusts10m; }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Current {
        private String time;
        private int interval;

        @JsonProperty("temperature_2m")
        private double temperature2m;

        @JsonProperty("relative_humidity_2m")
        private int relativeHumidity2m;

        @JsonProperty("apparent_temperature")
        private double apparentTemperature;

        @JsonProperty("is_day")
        private int isDay;

        private double precipitation;

        @JsonProperty("weather_code")
        private int weatherCode;

        @JsonProperty("cloud_cover")
        private int cloudCover;

        @JsonProperty("pressure_msl")
        private double pressureMsl;

        @JsonProperty("surface_pressure")
        private double surfacePressure;

        @JsonProperty("wind_speed_10m")
        private double windSpeed10m;

        @JsonProperty("wind_direction_10m")
        private int windDirection10m;

        @JsonProperty("wind_gusts_10m")
        private double windGusts10m;

        // Getters and Setters
        public String getTime() { return time; }
        public void setTime(String time) { this.time = time; }

        public int getInterval() { return interval; }
        public void setInterval(int interval) { this.interval = interval; }

        public double getTemperature2m() { return temperature2m; }
        public void setTemperature2m(double temperature2m) { this.temperature2m = temperature2m; }

        public int getRelativeHumidity2m() { return relativeHumidity2m; }
        public void setRelativeHumidity2m(int relativeHumidity2m) { this.relativeHumidity2m = relativeHumidity2m; }

        public double getApparentTemperature() { return apparentTemperature; }
        public void setApparentTemperature(double apparentTemperature) { this.apparentTemperature = apparentTemperature; }

        public int getIsDay() { return isDay; }
        public void setIsDay(int isDay) { this.isDay = isDay; }

        public double getPrecipitation() { return precipitation; }
        public void setPrecipitation(double precipitation) { this.precipitation = precipitation; }

        public int getWeatherCode() { return weatherCode; }
        public void setWeatherCode(int weatherCode) { this.weatherCode = weatherCode; }

        public int getCloudCover() { return cloudCover; }
        public void setCloudCover(int cloudCover) { this.cloudCover = cloudCover; }

        public double getPressureMsl() { return pressureMsl; }
        public void setPressureMsl(double pressureMsl) { this.pressureMsl = pressureMsl; }

        public double getSurfacePressure() { return surfacePressure; }
        public void setSurfacePressure(double surfacePressure) { this.surfacePressure = surfacePressure; }

        public double getWindSpeed10m() { return windSpeed10m; }
        public void setWindSpeed10m(double windSpeed10m) { this.windSpeed10m = windSpeed10m; }

        public int getWindDirection10m() { return windDirection10m; }
        public void setWindDirection10m(int windDirection10m) { this.windDirection10m = windDirection10m; }

        public double getWindGusts10m() { return windGusts10m; }
        public void setWindGusts10m(double windGusts10m) { this.windGusts10m = windGusts10m; }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DailyUnits {
        private String time;

        @JsonProperty("weather_code")
        private String weatherCode;

        @JsonProperty("temperature_2m_max")
        private String temperature2mMax;

        @JsonProperty("temperature_2m_min")
        private String temperature2mMin;

        @JsonProperty("apparent_temperature_max")
        private String apparentTemperatureMax;

        @JsonProperty("apparent_temperature_min")
        private String apparentTemperatureMin;

        private String sunrise;
        private String sunset;

        @JsonProperty("uv_index_max")
        private String uvIndexMax;

        @JsonProperty("precipitation_sum")
        private String precipitationSum;

        @JsonProperty("rain_sum")
        private String rainSum;

        @JsonProperty("wind_speed_10m_max")
        private String windSpeed10mMax;

        @JsonProperty("wind_direction_10m_dominant")
        private String windDirection10mDominant;

        // Getters and Setters
        public String getTime() { return time; }
        public void setTime(String time) { this.time = time; }

        public String getWeatherCode() { return weatherCode; }
        public void setWeatherCode(String weatherCode) { this.weatherCode = weatherCode; }

        public String getTemperature2mMax() { return temperature2mMax; }
        public void setTemperature2mMax(String temperature2mMax) { this.temperature2mMax = temperature2mMax; }

        public String getTemperature2mMin() { return temperature2mMin; }
        public void setTemperature2mMin(String temperature2mMin) { this.temperature2mMin = temperature2mMin; }

        public String getApparentTemperatureMax() { return apparentTemperatureMax; }
        public void setApparentTemperatureMax(String apparentTemperatureMax) { this.apparentTemperatureMax = apparentTemperatureMax; }

        public String getApparentTemperatureMin() { return apparentTemperatureMin; }
        public void setApparentTemperatureMin(String apparentTemperatureMin) { this.apparentTemperatureMin = apparentTemperatureMin; }

        public String getSunrise() { return sunrise; }
        public void setSunrise(String sunrise) { this.sunrise = sunrise; }

        public String getSunset() { return sunset; }
        public void setSunset(String sunset) { this.sunset = sunset; }

        public String getUvIndexMax() { return uvIndexMax; }
        public void setUvIndexMax(String uvIndexMax) { this.uvIndexMax = uvIndexMax; }

        public String getPrecipitationSum() { return precipitationSum; }
        public void setPrecipitationSum(String precipitationSum) { this.precipitationSum = precipitationSum; }

        public String getRainSum() { return rainSum; }
        public void setRainSum(String rainSum) { this.rainSum = rainSum; }

        public String getWindSpeed10mMax() { return windSpeed10mMax; }
        public void setWindSpeed10mMax(String windSpeed10mMax) { this.windSpeed10mMax = windSpeed10mMax; }

        public String getWindDirection10mDominant() { return windDirection10mDominant; }
        public void setWindDirection10mDominant(String windDirection10mDominant) { this.windDirection10mDominant = windDirection10mDominant; }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Daily {
        private String[] time;

        @JsonProperty("weather_code")
        private int[] weatherCode;

        @JsonProperty("temperature_2m_max")
        private double[] temperature2mMax;

        @JsonProperty("temperature_2m_min")
        private double[] temperature2mMin;

        @JsonProperty("apparent_temperature_max")
        private double[] apparentTemperatureMax;

        @JsonProperty("apparent_temperature_min")
        private double[] apparentTemperatureMin;

        private String[] sunrise;
        private String[] sunset;

        @JsonProperty("uv_index_max")
        private double[] uvIndexMax;

        @JsonProperty("precipitation_sum")
        private double[] precipitationSum;

        @JsonProperty("rain_sum")
        private double[] rainSum;

        @JsonProperty("wind_speed_10m_max")
        private double[] windSpeed10mMax;

        @JsonProperty("wind_direction_10m_dominant")
        private int[] windDirection10mDominant;

        // Getters and Setters
        public String[] getTime() { return time; }
        public void setTime(String[] time) { this.time = time; }

        public int[] getWeatherCode() { return weatherCode; }
        public void setWeatherCode(int[] weatherCode) { this.weatherCode = weatherCode; }

        public double[] getTemperature2mMax() { return temperature2mMax; }
        public void setTemperature2mMax(double[] temperature2mMax) { this.temperature2mMax = temperature2mMax; }

        public double[] getTemperature2mMin() { return temperature2mMin; }
        public void setTemperature2mMin(double[] temperature2mMin) { this.temperature2mMin = temperature2mMin; }

        public double[] getApparentTemperatureMax() { return apparentTemperatureMax; }
        public void setApparentTemperatureMax(double[] apparentTemperatureMax) { this.apparentTemperatureMax = apparentTemperatureMax; }

        public double[] getApparentTemperatureMin() { return apparentTemperatureMin; }
        public void setApparentTemperatureMin(double[] apparentTemperatureMin) { this.apparentTemperatureMin = apparentTemperatureMin; }

        public String[] getSunrise() { return sunrise; }
        public void setSunrise(String[] sunrise) { this.sunrise = sunrise; }

        public String[] getSunset() { return sunset; }
        public void setSunset(String[] sunset) { this.sunset = sunset; }

        public double[] getUvIndexMax() { return uvIndexMax; }
        public void setUvIndexMax(double[] uvIndexMax) { this.uvIndexMax = uvIndexMax; }

        public double[] getPrecipitationSum() { return precipitationSum; }
        public void setPrecipitationSum(double[] precipitationSum) { this.precipitationSum = precipitationSum; }

        public double[] getRainSum() { return rainSum; }
        public void setRainSum(double[] rainSum) { this.rainSum = rainSum; }

        public double[] getWindSpeed10mMax() { return windSpeed10mMax; }
        public void setWindSpeed10mMax(double[] windSpeed10mMax) { this.windSpeed10mMax = windSpeed10mMax; }

        public int[] getWindDirection10mDominant() { return windDirection10mDominant; }
        public void setWindDirection10mDominant(int[] windDirection10mDominant) { this.windDirection10mDominant = windDirection10mDominant; }
    }
}
