package com.mcp.weather.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * OpenMeteo 地理编码 API 响应模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GeocodingResponse {
    
    private Result[] results;
    
    @JsonProperty("generationtime_ms")
    private double generationTimeMs;
    
    public Result[] getResults() {
        return results;
    }
    
    public void setResults(Result[] results) {
        this.results = results;
    }
    
    public double getGenerationTimeMs() {
        return generationTimeMs;
    }
    
    public void setGenerationTimeMs(double generationTimeMs) {
        this.generationTimeMs = generationTimeMs;
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        private int id;
        private String name;
        private double latitude;
        private double longitude;
        private double elevation;
        
        @JsonProperty("feature_code")
        private String featureCode;
        
        @JsonProperty("country_code")
        private String countryCode;
        
        private String timezone;
        private int population;
        private String country;
        private String admin1;
        private String admin2;
        
        // Getters and Setters
        public int getId() {
            return id;
        }
        
        public void setId(int id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public double getLatitude() {
            return latitude;
        }
        
        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }
        
        public double getLongitude() {
            return longitude;
        }
        
        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }
        
        public double getElevation() {
            return elevation;
        }
        
        public void setElevation(double elevation) {
            this.elevation = elevation;
        }
        
        public String getFeatureCode() {
            return featureCode;
        }
        
        public void setFeatureCode(String featureCode) {
            this.featureCode = featureCode;
        }
        
        public String getCountryCode() {
            return countryCode;
        }
        
        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }
        
        public String getTimezone() {
            return timezone;
        }
        
        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }
        
        public int getPopulation() {
            return population;
        }
        
        public void setPopulation(int population) {
            this.population = population;
        }
        
        public String getCountry() {
            return country;
        }
        
        public void setCountry(String country) {
            this.country = country;
        }
        
        public String getAdmin1() {
            return admin1;
        }
        
        public void setAdmin1(String admin1) {
            this.admin1 = admin1;
        }
        
        public String getAdmin2() {
            return admin2;
        }
        
        public void setAdmin2(String admin2) {
            this.admin2 = admin2;
        }
    }
}
