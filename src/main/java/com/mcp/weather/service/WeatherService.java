package com.mcp.weather.service;

import com.google.gson.Gson;
import com.mcp.weather.model.GeocodingResponse;
import com.mcp.weather.model.WeatherResponse;
import com.mcp.weather.util.PinyinUtil;
import com.mcp.weather.util.WeatherCodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 天气服务类
 * 使用 OpenMeteo API 提供天气查询功能
 */
@Service
public class WeatherService {

    private static final Logger logger = LoggerFactory.getLogger(WeatherService.class);

    private final WebClient webClient;
    private final WebClient geocodingClient;

    public WeatherService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
                .baseUrl("https://api.open-meteo.com/v1")
                .build();

        this.geocodingClient = webClientBuilder
                .baseUrl("https://geocoding-api.open-meteo.com/v1")
                .build();
    }

    @Tool(description = "根据城市名称获取当前天气信息")
    public String getCurrentWeather(@ToolParam(description = "城市名称，例如：北京、上海、New York、London") String cityName) {

        logger.info("获取城市 {} 的当前天气信息", cityName);

        try {
            // 1. 先通过地理编码获取城市的经纬度
            GeocodingResponse.Result location = getLocationByCity(PinyinUtil.toPinyin(cityName));
            if (location == null) {
                return "抱歉，未找到城市 \"" + cityName + "\" 的位置信息。请检查城市名称是否正确。";
            }

            // 2. 使用经纬度获取天气信息
            WeatherResponse weatherResponse = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/forecast")
                            .queryParam("latitude", location.getLatitude())
                            .queryParam("longitude", location.getLongitude())
                            .queryParam("current", "temperature_2m,relative_humidity_2m,apparent_temperature," +
                                    "is_day,precipitation,weather_code,cloud_cover,pressure_msl," +
                                    "wind_speed_10m,wind_direction_10m,wind_gusts_10m")
                            .queryParam("timezone", "auto")
                            .build())
                    .retrieve()
                    .bodyToMono(WeatherResponse.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (weatherResponse == null || weatherResponse.getCurrent() == null) {
                return "获取天气信息失败，请稍后重试。";
            }

            return formatCurrentWeather(location, weatherResponse);

        } catch (Exception e) {
            logger.error("获取天气信息时发生错误: {}", e.getMessage(), e);
            return "获取天气信息失败：" + e.getMessage();
        }
    }

    @Tool(description = "根据城市名称获取未来7天的天气预报")
    public String getWeatherForecast(@ToolParam(description = "城市名称，例如：北京、上海、New York、London") String cityName,
                                     @ToolParam(description = "预报天数，1-7天，默认为7天") Integer days) {

        if (days == null || days < 1 || days > 7) {
            days = 7;
        }

        logger.info("获取城市 {} 未来 {} 天的天气预报", cityName, days);

        try {
            // 1. 先通过地理编码获取城市的经纬度
            GeocodingResponse.Result location = getLocationByCity(PinyinUtil.toPinyin(cityName));
            if (location == null) {
                return "抱歉，未找到城市 \"" + cityName + "\" 的位置信息。请检查城市名称是否正确。";
            }

            // 2. 使用经纬度获取天气预报
            LocalDate endDate = LocalDate.now().plusDays(days - 1);

            WeatherResponse weatherResponse = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/forecast")
                            .queryParam("latitude", location.getLatitude())
                            .queryParam("longitude", location.getLongitude())
                            .queryParam("daily", "weather_code,temperature_2m_max,temperature_2m_min," +
                                    "apparent_temperature_max,apparent_temperature_min,sunrise,sunset," +
                                    "uv_index_max,precipitation_sum,rain_sum," +
                                    "wind_speed_10m_max,wind_direction_10m_dominant")
                            .queryParam("timezone", "auto")
                            .queryParam("start_date", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE))
                            .queryParam("end_date", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                            .build())
                    .retrieve()
                    .bodyToMono(WeatherResponse.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (weatherResponse == null || weatherResponse.getDaily() == null) {
                return "获取天气预报失败，请稍后重试。";
            }

            return formatWeatherForecast(location, weatherResponse, days);

        } catch (Exception e) {
            logger.error("获取天气预报时发生错误: {}", e.getMessage(), e);
            return "获取天气预报失败：" + e.getMessage();
        }
    }

    @Tool(description = "根据经纬度获取当前天气信息")
    public String getWeatherByCoordinates(@ToolParam(description = "纬度，例如：39.9042") double latitude,
                                          @ToolParam(description = "经度，例如：116.4074") double longitude) {

        logger.info("获取坐标 ({}, {}) 的天气信息", latitude, longitude);

        try {
            WeatherResponse weatherResponse = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/forecast")
                            .queryParam("latitude", latitude)
                            .queryParam("longitude", longitude)
                            .queryParam("current", "temperature_2m,relative_humidity_2m,apparent_temperature," +
                                    "is_day,precipitation,weather_code,cloud_cover,pressure_msl," +
                                    "wind_speed_10m,wind_direction_10m,wind_gusts_10m")
                            .queryParam("timezone", "auto")
                            .build())
                    .retrieve()
                    .bodyToMono(WeatherResponse.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (weatherResponse == null || weatherResponse.getCurrent() == null) {
                return "获取天气信息失败，请稍后重试。";
            }

            // 创建一个虚拟的位置对象
            GeocodingResponse.Result location = new GeocodingResponse.Result();
            location.setLatitude(latitude);
            location.setLongitude(longitude);
            location.setName("指定位置");

            return formatCurrentWeather(location, weatherResponse);

        } catch (Exception e) {
            logger.error("获取天气信息时发生错误: {}", e.getMessage(), e);
            return "获取天气信息失败：" + e.getMessage();
        }
    }

    /**
     * 通过城市名称获取地理位置信息
     */
    private GeocodingResponse.Result getLocationByCity(String cityName) {
        try {
            GeocodingResponse response = geocodingClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/search")
                            .queryParam("name", cityName)
                            .queryParam("count", 1)
                            .queryParam("language", "zh")
                            .queryParam("format", "json")
                            .build())
                    .retrieve()
                    .bodyToMono(GeocodingResponse.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (response != null && response.getResults() != null && response.getResults().length > 0) {
                return response.getResults()[0];
            }

            return null;
        } catch (Exception e) {
            logger.error("获取城市位置信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 格式化当前天气信息
     */
    private String formatCurrentWeather(GeocodingResponse.Result location, WeatherResponse weatherResponse) {
        WeatherResponse.Current current = weatherResponse.getCurrent();

        StringBuilder sb = new StringBuilder();
        sb.append("🌍 ").append(location.getName());
        if (location.getCountry() != null) {
            sb.append(", ").append(location.getCountry());
        }
        sb.append(" 当前天气\n");
        sb.append("📍 位置: ").append(String.format("%.4f°N, %.4f°E", location.getLatitude(), location.getLongitude())).append("\n");
        sb.append("🕐 更新时间: ").append(current.getTime()).append("\n\n");

        // 基本天气信息
        sb.append("🌡️ 温度: ").append(current.getTemperature2m()).append("°C");
        sb.append(" (体感: ").append(current.getApparentTemperature()).append("°C)\n");

        sb.append("🌤️ 天气: ").append(WeatherCodeUtil.getWeatherDescription(current.getWeatherCode()));
        sb.append(current.getIsDay() == 1 ? " (白天)" : " (夜晚)").append("\n");

        sb.append("💧 湿度: ").append(current.getRelativeHumidity2m()).append("%");
        sb.append(" (").append(WeatherCodeUtil.getHumidityLevel(current.getRelativeHumidity2m())).append(")\n");

        sb.append("☁️ 云量: ").append(current.getCloudCover()).append("%\n");

        // 风力信息
        sb.append("💨 风力: ").append(WeatherCodeUtil.getWindDirection(current.getWindDirection10m()));
        sb.append(" ").append(current.getWindSpeed10m()).append(" km/h");
        sb.append(" (").append(WeatherCodeUtil.getWindLevel(current.getWindSpeed10m())).append(")\n");

        if (current.getWindGusts10m() > 0) {
            sb.append("🌪️ 阵风: ").append(current.getWindGusts10m()).append(" km/h\n");
        }

        // 气压和降水
        sb.append("🔽 气压: ").append(current.getPressureMsl()).append(" hPa\n");

        if (current.getPrecipitation() > 0) {
            sb.append("🌧️ 降水量: ").append(current.getPrecipitation()).append(" mm\n");
        }

        return sb.toString();
    }

    /**
     * 格式化天气预报信息
     */
    private String formatWeatherForecast(GeocodingResponse.Result location, WeatherResponse weatherResponse, int days) {
        WeatherResponse.Daily daily = weatherResponse.getDaily();

        StringBuilder sb = new StringBuilder();
        sb.append("🌍 ").append(location.getName());
        if (location.getCountry() != null) {
            sb.append(", ").append(location.getCountry());
        }
        sb.append(" 未来").append(days).append("天天气预报\n");
        sb.append("📍 位置: ").append(String.format("%.4f°N, %.4f°E", location.getLatitude(), location.getLongitude())).append("\n\n");

        for (int i = 0; i < Math.min(days, daily.getTime().length); i++) {
            sb.append("📅 ").append(daily.getTime()[i]);
            if (i == 0) {
                sb.append(" (今天)");
            } else if (i == 1) {
                sb.append(" (明天)");
            }
            sb.append("\n");

            sb.append("🌡️ 温度: ").append(daily.getTemperature2mMin()[i]).append("°C ~ ")
                    .append(daily.getTemperature2mMax()[i]).append("°C\n");

            sb.append("🌤️ 天气: ").append(WeatherCodeUtil.getWeatherDescription(daily.getWeatherCode()[i])).append("\n");

            if (daily.getPrecipitationSum()[i] > 0) {
                sb.append("🌧️ 降水: ").append(daily.getPrecipitationSum()[i]).append(" mm\n");
            }

            sb.append("💨 风力: ").append(WeatherCodeUtil.getWindDirection(daily.getWindDirection10mDominant()[i]));
            sb.append(" ").append(daily.getWindSpeed10mMax()[i]).append(" km/h");
            sb.append(" (").append(WeatherCodeUtil.getWindLevel(daily.getWindSpeed10mMax()[i])).append(")\n");

            if (daily.getUvIndexMax()[i] > 0) {
                sb.append("☀️ 紫外线: ").append(daily.getUvIndexMax()[i]);
                sb.append(" (").append(WeatherCodeUtil.getUvLevel(daily.getUvIndexMax()[i])).append(")\n");
            }

            sb.append("🌅 日出: ").append(daily.getSunrise()[i].substring(11));
            sb.append(" 🌇 日落: ").append(daily.getSunset()[i].substring(11)).append("\n");

            if (i < days - 1) {
                sb.append("\n");
            }
        }

        return sb.toString();
    }
}
